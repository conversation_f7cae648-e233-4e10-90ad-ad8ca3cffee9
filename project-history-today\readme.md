# 项目：历史上的今天 (Project: Today in History) - v1.1

本文件是 “历史上的今天” 内容创作项目的核心指导手册和行动清单。所有操作均围绕此文件定义的标准和流程进行。

---

## 1. 项目使命 (Project Mission)

* **目标**：将厚重的历史，转化为轻松、有趣、有记忆点的每日图文故事。
* **愿景**：成为读者每日获取知识、激发思考、感受文化魅力的精神食粮。
* **核心平台**：微信公众号。

---

## 2. 内容定位 (Content Identity)

* **人设 (Role)**：一位博学、风趣、会讲故事的历史老师。
* **风格 (Tone)**：严谨与趣味并存。用现代人的视角和通俗的语言，解读历史事件，但对核心事实保持绝对敬畏。
* **视觉风格 (Visual Style)**：纪实感与艺术感结合。配图力求有历史氛围，或具有高度的艺术概括性，审美在线。
* **互动准则 (Interaction Rules)**：
    * **评论回复**：对有价值的、善意的评论，应以“历史老师”的人设进行回复。保持礼貌、风趣和专业。
    * **负面处理**：对于不友好的评论，选择性忽略或冷静、中立地回应事实部分。不陷入争吵。
* **品牌资产 (Brand Assets)**：
    * **公众号头像/Logo**：[此处可记录Logo的设计理念或文件链接]
    * **标准配色**：[定义1-2个用于图片点缀或模板的品牌色，如：复古棕 #8B4513]

---

## 3. 标准作业流程 (SOP - Standard Operating Procedure)

这是每日内容生产的“流水线”，严格遵循以下步骤以保证效率和质量。

> **核心效率原则：批量处理 (Batching)。** 建议每周安排1-2个固定的“生产日”，集中完成未来3-7天的所有内容（从选题到稿件、配图），然后在微信后台设置好定时发布。避免每天重复启动和切换任务，最大化节省精力。

### **阶段一：内容生产 (可批量操作)**

**✅ 步骤1：选题 (Topic Selection)**

1.  **来源**：使用AI工具（Kimi, Copilot）或维基百科的“历史上的今天”页面。
2.  **标准**：
    * **趣味性优先**：选择大众更可能感兴趣的、有故事性的事件（如科技、发明、文化、奇闻）。
    * **正向价值**：优先选择具有启发性、纪念意义的事件。
    * **规避风险**：谨慎处理有巨大争议或过于敏感的近现代史事件。对不确定的选题，优先在“知乎”等平台搜索相关讨论，观察舆论风向，避免触碰不可预知的“雷区”。
3.  **工具指令**：`"请告诉我，[今天日期] 在历史上发生了哪些有趣或重要的事件？请列出3-5个，并简单说明。"`

**✅ 步骤2：稿件生成 (Text Generation)**

1.  **核心Prompt模板**：
    ```
    你现在是一位博学且风趣的历史老师，正在为你的微信公众号“历史上的今天”撰写内容。
    
    请围绕以下主题，撰写一篇800字左右的图文稿件：
    - **事件**：[此处填写选定的历史事件]
    - **关键要素**：[可选，填写希望包含的人物、背景、细节]
    - **要求**：
      1. 开头要能迅速吸引读者，制造悬念或引出共鸣。
      2. 叙事生动，多用比喻和故事化的描述，避免像教科书一样罗列事实。
      3. 结尾需要有总结或升华，引发读者思考，或者点出该事件对今天的深远影响。
      4. 文风要兼具严谨与趣味。
    ```
2.  **产出**：将AI生成的稿件复制到文本编辑器中，进行人工润色和微调。

**✅ 步骤3：配图生成 (Image Generation)**

1.  **数量**：每篇文章至少需要 **2-3** 张高质量配图（1张封面，1-2张内文图）。
2.  **核心Prompt模板**：
    * **封面图 (要求高)**：`"生成一张高质量的照片/油画，主题是 [事件核心场景]。风格要求 [纪实摄影/复古风格/水墨画风]，构图要 [有视觉冲击力/意境深远]，适合用作文章封面。画幅比例16:9。"`
    * **内文图**：`"生成一张图片，描绘 [事件中的某个具体细节或人物]。风格与封面保持一致。"`
3.  **工具**：Microsoft Copilot, SeaArt.ai, Leonardo.Ai。

**✅ 步骤4：事实核查 (Fact-Checking) - \[不可跳过！]**

1.  **核查对象**：AI生成稿件中的所有 **关键信息**。
2.  **清单**：
    * [ ] **人名**：拼写和身份是否正确？
    * [ ] **时间**：年份、月、日是否准确？
    * [ ] **地点**：地理位置是否正确？
    * [ ] **核心数据/引言**：是否与权威资料相符？
3.  **核查工具**：至少使用一个与AI不同的信息源进行交叉验证，如百度百科、维基百科等。

### **阶段二：发布、归档与迭代**

**✅ 步骤5：排版与发布 (WeChat Publishing)**

1.  **标题**：基于文稿内容，拟定3-5个吸引人的标题，选择最佳一个。可使用AI辅助：`"请为以下文章，起5个吸引人的微信公众号标题：[粘贴文稿]"`。
2.  **格式标准**：
    * **字体**：默认字体。
    * **字号**：正文 `15px`，注释或引用 `14px`。
    * **行间距**：`1.75`。
    * **段间距**：段前段后留空。
    * **引导**：在文末放置公众号二维码，并附上引导关注的话术。
3.  **预览**：在手机上多次预览，确保阅读体验流畅。
4.  **定时发布**：选择一个固定的时间发布，培养用户阅读习惯（如：每晚21:00）。

**✅ 步骤6：素材归档 (Asset Archiving)**

1.  **目的**：为未来的内容复用和项目复盘建立数据库。
2.  **文件夹结构**：在`project-history-today`目录下，按日期创建文件夹，如 `2025-06-06-D-Day`。
3.  **文件夹内容**：
    * `final_article.md` (最终发布的文章Markdown稿)
    * `image_cover.png` (封面图)
    * `image_01.png`, `image_02.png` (内文图)
    * `prompts.txt` (本次使用的所有Prompt记录)
    * `notes.md` (本次创作的心得、遇到的问题、改进点)

**✅ 步骤7：每周复盘 (Weekly Review & Iteration)**

1.  **数据分析**：每周日晚，查看本周所有文章的后台数据。
    * [ ] 哪篇文章的**阅读量/分享率**最高？为什么？（标题？选题？封面图？）
    * [ ] 读者的**留言/评论**集中在哪些方面？他们对什么感兴趣？
2.  **SOP优化**：根据数据分析的结果，更新本文档（SOP）。例如：如果发现“科技发明”类主题阅读量普遍更高，就在“选题标准”中提升它的优先级。
3.  **归档记录**：在项目根目录创建一个 `weekly-review.md` 文件，简单记录每周的复盘结论。

---

## 4. 工具清单 (Tool Stack)

* **文本AI**：Kimi.ai, Microsoft Copilot
* **图片AI**：Microsoft Copilot, SeaArt.ai
* **事实核查**：百度百科, 维基百科
* **排版/发布**：微信公众号平台
* **项目管理/归档**：GitHub

---

## 5. 阶段性目标 (Milestones)

* **第一个月 (启动期)**：
    * [ ] 完成至少20篇内容的发布。
    * [ ] 跑通并优化SOP流程，形成稳定的v1.0版本。
    * [ ] 积累首批100个种子用户。
* **第二个月 (优化期)**：
    * [ ] 分析后台数据，找出最受欢迎的内容类型。
    * [ ] 尝试与读者互动，建立社群雏形。
* **第三个月 (增长期)**：
    * [ ] 探索其他平台分发的可行性（如小红书、知乎）。

