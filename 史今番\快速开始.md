# 🚀 快速开始指南

## 第一步：安装依赖

### 方法一：使用安装脚本（推荐）
```bash
python install_dependencies.py
```

### 方法二：手动安装
```bash
pip install google-generativeai
```

## 第二步：获取API Key

1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 登录Google账号
3. 点击 "Get API Key" 创建新的API Key
4. 复制生成的API Key

## 第三步：配置参数

### 方法一：编辑配置文件（推荐）
编辑 `config.json` 文件：

```json
{
  "api_keys": [
    "你的第一个Gemini API Key",
    "你的第二个Gemini API Key（可选）",
    "你的第三个Gemini API Key（可选）"
  ],
  "start_date": "2025-07-05",
  "end_date": "2025-07-15",
  "versions_per_date": 3,
  "max_retries": 3,
  "retry_delay": 5,
  "request_delay": 2
}
```

### 方法二：直接修改程序代码
打开 `generate_gemini_content.py` 文件，在顶部找到 `DEFAULT_CONFIG` 部分直接修改：

```python
# ==================== 配置信息 ====================
DEFAULT_CONFIG = {
    "api_keys": [
        "你的第一个Gemini API Key",
        "你的第二个Gemini API Key",
        "你的第三个Gemini API Key"
    ],
    "start_date": "2025-07-05",      # 开始日期
    "end_date": "2025-07-15",        # 结束日期
    "versions_per_date": 3,          # 每日生成版本数
    # ... 其他配置
}
```

**💡 配置说明：**
- `api_keys`: 可以配置多个API Key实现轮询，提高稳定性
- `start_date/end_date`: 生成内容的日期范围
- `versions_per_date`: 每个日期生成几个不同版本
- `max_retries`: API调用失败时的最大重试次数
- `retry_delay`: 重试间隔时间（秒）
- `request_delay`: 请求之间的间隔时间（秒）

## 第四步：运行程序

### Windows用户
双击运行 `run.bat` 文件

### 或者使用命令行
```bash
python generate_gemini_content.py
```

## 第五步：智能生成过程

程序运行时会自动：

1. **📁 创建目录结构**：根据配置的日期范围自动创建所有日期文件夹
2. **🔍 智能检测**：扫描已存在的文件，只生成缺失的版本
3. **📊 显示统计**：展示详细的任务分析和进度信息
4. **🚀 高效生成**：只调用API生成真正需要的内容

### 示例运行输出：
```
📅 日期范围: 2025-07-05 到 2025-07-15
📝 每日版本数: 3
🔑 API Keys数量: 2
📁 创建日期目录...
📊 任务分析:
   总可能任务数: 33
   已完成任务数: 15
   待生成任务数: 18
📋 缺失内容详情:
   20250705: 缺失版本 [2]
   20250706: 缺失版本 [1, 3]
   20250707: 缺失版本 [1, 2, 3]
```

## 第六步：查看结果

程序运行完成后，会在对应日期文件夹中生成内容：

```
20250705/
├── 1.html  # 第一个版本的HTML图文
├── 1.txt   # 第一个版本的视频文案
├── 2.html  # 第二个版本的HTML图文
├── 2.txt   # 第二个版本的视频文案
├── 3.html  # 第三个版本的HTML图文
└── 3.txt   # 第三个版本的视频文案
```

## 🌟 新功能亮点

### ✨ 智能版本检测
- **自动跳过已存在的文件**：如果某个日期已有版本1和3，只会生成缺失的版本2
- **精确统计**：显示每个日期具体缺失哪些版本
- **高效生成**：避免重复调用API，节省时间和配额

### 📁 自动目录管理
- **预先创建目录**：根据配置的日期范围自动创建所有文件夹
- **结构清晰**：每个日期独立目录，便于管理
- **无需手动操作**：完全自动化的目录结构

### 🔧 灵活配置方式
- **双重配置**：既可以修改config.json，也可以直接修改代码中的DEFAULT_CONFIG
- **配置统一**：所有重要配置都放在文件顶部，一目了然
- **智能合并**：config.json会自动合并默认配置，确保完整性

### 📊 详细进度显示
- **任务分析**：显示总任务数、已完成数、待生成数
- **实时统计**：每个日期的具体完成情况
- **最终报告**：生成完成后的详细统计信息

## 🔧 常见问题

### Q: API Key无效怎么办？
A: 检查以下几点：
- API Key是否正确复制（没有多余空格）
- API Key是否已激活
- Google账号是否有API使用权限

### Q: 程序运行很慢怎么办？
A: 这是正常现象，因为：
- AI生成内容需要时间
- 程序内置了请求间隔避免触发限制
- 可以在config.json中调整`request_delay`参数

### Q: 程序中断了怎么办？
A: 不用担心！程序支持智能断点续跑：
- 重新运行程序即可
- 已生成的内容不会重复生成
- 程序会自动检测并只生成缺失的版本

### Q: 想修改生成的日期范围怎么办？
A: 有两种方法：

**方法1 - 编辑config.json：**
```json
{
  "start_date": "2025-07-01",  # 开始日期
  "end_date": "2025-07-31",    # 结束日期
  "versions_per_date": 3       # 每日版本数
}
```

**方法2 - 直接修改代码：**
在 `generate_gemini_content.py` 顶部修改 `DEFAULT_CONFIG`

### Q: 如何只生成特定日期的缺失版本？
A: 程序会自动检测！例如：
- 如果20250705只有1.html/1.txt和3.html/3.txt
- 程序会自动只生成缺失的版本2
- 无需手动指定，完全智能化

## 📊 监控进度

- **实时进度**: 查看控制台输出
- **详细日志**: 查看 `gemini_content_generator.log`
- **进度文件**: 查看 `progress.json`

## 🎯 高级用法

### 自定义prompt
编辑 `prompt.md` 文件来自定义AI生成的内容风格和要求

### 批量处理大量日期
程序支持处理大量日期，建议：
- 配置多个API Key
- 适当增加`request_delay`
- 定期检查API配额使用情况

### 错误恢复
如果遇到错误：
1. 查看日志文件了解具体错误
2. 检查网络连接和API配额
3. 重新运行程序（支持断点续跑）

## 📞 技术支持

如果遇到问题：
1. 查看 `README.md` 详细文档
2. 运行 `python test_generator.py` 进行功能测试
3. 检查 `gemini_content_generator.log` 日志文件

---

🎉 **恭喜！你已经成功配置了Gemini批量内容生成器！**

现在可以开始批量生成高质量的AI内容了！
