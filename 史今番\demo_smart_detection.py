#!/usr/bin/env python3
"""
智能检测功能演示脚本
展示新的智能版本检测和目录创建功能
"""

import os
import shutil
from generate_gemini_content import GeminiContentGenerator

def create_demo_files():
    """创建演示文件"""
    print("🎭 创建演示环境...")
    
    # 创建一些示例文件来模拟部分完成的情况
    demo_dates = ["20250705", "20250706", "20250707"]
    
    for date_str in demo_dates:
        folder = os.path.join(os.getcwd(), date_str)
        os.makedirs(folder, exist_ok=True)
    
    # 20250705: 只有版本1和3，缺少版本2
    with open("20250705/1.html", 'w', encoding='utf-8') as f:
        f.write("<html><body><h1>演示内容 - 20250705 版本1</h1></body></html>")
    with open("20250705/1.txt", 'w', encoding='utf-8') as f:
        f.write("演示视频文案 - 20250705 版本1")
    
    with open("20250705/3.html", 'w', encoding='utf-8') as f:
        f.write("<html><body><h1>演示内容 - 20250705 版本3</h1></body></html>")
    with open("20250705/3.txt", 'w', encoding='utf-8') as f:
        f.write("演示视频文案 - 20250705 版本3")
    
    # 20250706: 只有版本2，缺少版本1和3
    with open("20250706/2.html", 'w', encoding='utf-8') as f:
        f.write("<html><body><h1>演示内容 - 20250706 版本2</h1></body></html>")
    with open("20250706/2.txt", 'w', encoding='utf-8') as f:
        f.write("演示视频文案 - 20250706 版本2")
    
    # 20250707: 完全没有文件，缺少所有版本
    
    print("✅ 演示环境创建完成")

def demo_smart_detection():
    """演示智能检测功能"""
    print("\n🔍 智能检测功能演示")
    print("=" * 50)
    
    # 创建生成器实例
    generator = GeminiContentGenerator()
    
    # 演示日期
    demo_dates = ["20250705", "20250706", "20250707"]
    versions_per_date = 3
    
    print("📊 检测各日期缺失的版本:")
    
    for date_str in demo_dates:
        missing_versions = generator.get_missing_versions(date_str, versions_per_date)
        existing_versions = []
        
        for idx in range(1, versions_per_date + 1):
            if generator.check_existing_content(date_str, idx):
                existing_versions.append(idx)
        
        print(f"📅 {date_str}:")
        print(f"   ✅ 已有版本: {existing_versions if existing_versions else '无'}")
        print(f"   ❌ 缺失版本: {missing_versions if missing_versions else '无'}")
        
        if missing_versions:
            print(f"   🔄 需要生成: {len(missing_versions)} 个版本")
        else:
            print(f"   🎉 该日期所有版本已完成")
        print()

def demo_directory_creation():
    """演示目录创建功能"""
    print("📁 目录创建功能演示")
    print("=" * 50)
    
    # 删除一些目录来演示创建功能
    test_dates = ["20250708", "20250709", "20250710"]
    
    print("🗑️ 清理测试目录...")
    for date_str in test_dates:
        folder = os.path.join(os.getcwd(), date_str)
        if os.path.exists(folder):
            shutil.rmtree(folder)
    
    # 创建生成器实例
    generator = GeminiContentGenerator()
    
    print("🏗️ 演示目录创建...")
    generator.create_date_directories(test_dates)
    
    print("\n📋 验证目录创建结果:")
    for date_str in test_dates:
        folder = os.path.join(os.getcwd(), date_str)
        if os.path.exists(folder):
            print(f"   ✅ {date_str}: 目录已创建")
        else:
            print(f"   ❌ {date_str}: 目录创建失败")

def cleanup_demo():
    """清理演示文件"""
    print("\n🧹 清理演示文件...")
    
    demo_dates = ["20250705", "20250706", "20250707", "20250708", "20250709", "20250710"]
    
    for date_str in demo_dates:
        folder = os.path.join(os.getcwd(), date_str)
        if os.path.exists(folder):
            try:
                shutil.rmtree(folder)
                print(f"   🗑️ 已删除: {date_str}")
            except Exception as e:
                print(f"   ❌ 删除失败 {date_str}: {e}")
    
    # 清理进度文件
    if os.path.exists("progress.json"):
        os.remove("progress.json")
        print("   🗑️ 已删除: progress.json")

def main():
    """主演示函数"""
    print("🎪 Gemini内容生成器 - 智能检测功能演示")
    print("=" * 60)
    
    try:
        # 创建演示环境
        create_demo_files()
        
        # 演示智能检测
        demo_smart_detection()
        
        # 演示目录创建
        demo_directory_creation()
        
        print("=" * 60)
        print("🎉 演示完成！")
        print()
        print("💡 新功能说明:")
        print("1. ✅ 配置信息已统一放在文件顶部，方便修改")
        print("2. ✅ 程序会根据配置自动创建所有日期目录")
        print("3. ✅ 智能检测缺失版本，只生成需要的内容")
        print("4. ✅ 修复了prompt.md文件路径问题")
        print("5. ✅ 详细的进度显示和统计信息")
        print()
        print("🚀 现在你可以:")
        print("- 直接修改程序顶部的DEFAULT_CONFIG")
        print("- 或者编辑config.json文件")
        print("- 运行程序时会自动跳过已存在的文件")
        print("- 支持断点续跑，中断后重新运行即可")
        
    except Exception as e:
        print(f"❌ 演示过程出错: {e}")
    
    finally:
        # 询问是否清理
        print()
        choice = input("🤔 是否清理演示文件? (y/N): ").strip().lower()
        if choice == 'y':
            cleanup_demo()
            print("✅ 清理完成")
        else:
            print("📁 演示文件已保留")

if __name__ == "__main__":
    main()
