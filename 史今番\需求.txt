需求重述（架构视角）
多API Key轮询与高可用性

脚本内置多个Google Gemini API Key，自动轮询使用，遇到配额/速率/异常时自动切换，确保批量生成任务不中断。
批量内容生成

针对指定日期区间（如2025年7月5日至7月15日），每个日期自动生成3个不同风格的内容版本。
AI调用与内容分离

每次调用Gemini 2.5 Pro模型，将prompt（如prompt.md内容）和目标日期一并发送，要求分别调用AI 3次
AI返回内容需结构化分离为：
HTML渲染图文（如1.html、2.html、3.html）
视频文案（如1.txt、2.txt、3.txt）
自动化文件与目录管理

每个日期自动创建独立文件夹（如20250705、20250706等）。
每个版本的内容分别保存为对应的html和txt文件，命名规范（如1.html、1.txt、2.html、2.txt、3.html、3.txt）。
健壮性与易用性

支持异常处理（如API Key失效、网络异常、AI返回格式不符等），自动重试与日志提示。
只需运行脚本，无需手动输入API Key或日期，全部自动化。
架构要点
API Key池管理：实现API Key轮询与失效检测，保证高并发和高可用。
任务调度：循环遍历日期和版本，自动分配任务，支持断点续跑。
AI交互层：封装Gemini 2.5 Pro的调用逻辑，支持内容结构化解析。
文件系统层：自动创建目录、保存文件，确保命名规范和数据隔离。
异常与日志：详细的错误捕获、重试机制和进度日志，便于追踪和维护。
目标效果
一键批量生成指定日期区间的多版本AI内容，全部自动保存到本地，结构清晰、可直接使用。
支持大规模内容生产，API Key自动轮询，极大提升稳定性和效率。
适合内容创作、批量生产、自动化运营等场景。