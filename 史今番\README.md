# Gemini 批量内容生成器

这是一个基于Google Gemini API的批量内容生成工具，能够自动生成指定日期范围内的多版本AI内容。

## 功能特点

### 🔄 多API Key轮询与高可用性
- 支持配置多个Google Gemini API Key
- 自动轮询使用，遇到配额/速率/异常时自动切换
- 确保批量生成任务不中断

### 📅 批量内容生成
- 针对指定日期区间，每个日期自动生成3个不同风格的内容版本
- 支持自定义日期范围和版本数量

### 🤖 AI调用与内容分离
- 使用Gemini 2.0 Flash模型
- 自动解析AI返回内容，分离为HTML和TXT格式
- 结构化保存为独立文件

### 📁 自动化文件与目录管理
- 每个日期自动创建独立文件夹（如20250705、20250706等）
- 规范化文件命名（1.html、1.txt、2.html、2.txt、3.html、3.txt）

### 🛡️ 健壮性与易用性
- 完善的异常处理（API Key失效、网络异常、AI返回格式不符等）
- 自动重试机制与详细日志
- 支持断点续跑，避免重复生成
- 全自动化运行，无需手动干预

## 安装依赖

```bash
pip install google-generativeai
```

## 配置说明

### 1. 编辑配置文件 `config.json`

```json
{
  "api_keys": [
    "你的第一个Gemini API Key",
    "你的第二个Gemini API Key", 
    "你的第三个Gemini API Key"
  ],
  "prompt_path": "prompt.md",
  "start_date": "2025-07-05",
  "end_date": "2025-07-15",
  "versions_per_date": 3,
  "max_retries": 3,
  "retry_delay": 5,
  "request_delay": 2
}
```

**配置项说明：**
- `api_keys`: Google Gemini API Key列表，支持多个Key轮询
- `prompt_path`: prompt文件路径
- `start_date`: 开始日期（YYYY-MM-DD格式）
- `end_date`: 结束日期（YYYY-MM-DD格式）
- `versions_per_date`: 每个日期生成的版本数量
- `max_retries`: 最大重试次数
- `retry_delay`: 重试间隔（秒）
- `request_delay`: 请求间隔（秒）

### 2. 获取Gemini API Key

1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 登录Google账号
3. 创建新的API Key
4. 将API Key填入配置文件

## 使用方法

### 1. 运行程序

```bash
python generate_gemini_content.py
```

### 2. 程序执行流程

1. **加载配置**: 读取config.json和prompt.md
2. **生成任务列表**: 根据日期范围和版本数量生成任务
3. **检查进度**: 加载之前的进度，支持断点续跑
4. **批量生成**: 
   - 遍历每个日期和版本
   - 调用Gemini API生成内容
   - 解析并保存HTML和TXT文件
   - 记录进度和日志
5. **完成**: 输出最终统计信息

### 3. 输出结构

```
项目目录/
├── 20250705/
│   ├── 1.html
│   ├── 1.txt
│   ├── 2.html
│   ├── 2.txt
│   ├── 3.html
│   └── 3.txt
├── 20250706/
│   ├── 1.html
│   ├── 1.txt
│   ├── 2.html
│   ├── 2.txt
│   ├── 3.html
│   └── 3.txt
├── ...
├── config.json
├── prompt.md
├── progress.json (进度文件)
└── gemini_content_generator.log (日志文件)
```

## 日志与监控

- **日志文件**: `gemini_content_generator.log`
- **进度文件**: `progress.json`
- **控制台输出**: 实时显示生成进度

## 异常处理

程序具备完善的异常处理机制：

1. **API Key失效**: 自动切换到下一个可用Key
2. **配额限制**: 等待后自动重试
3. **网络异常**: 自动重试，记录详细日志
4. **格式解析错误**: 兜底处理，确保内容不丢失
5. **文件保存失败**: 记录错误，继续处理其他任务

## 断点续跑

程序支持断点续跑功能：
- 自动检测已完成的任务
- 跳过已存在的文件
- 从中断处继续执行
- 保持进度记录

## 注意事项

1. **API配额**: 注意Gemini API的使用配额限制
2. **请求频率**: 程序已内置请求间隔，避免触发速率限制
3. **文件权限**: 确保程序有创建文件夹和文件的权限
4. **网络连接**: 确保网络连接稳定

## 故障排除

### 常见问题

1. **API Key无效**
   - 检查API Key是否正确
   - 确认API Key是否已激活
   - 检查API配额是否充足

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置
   - 尝试使用代理

3. **文件保存失败**
   - 检查磁盘空间
   - 确认文件权限
   - 检查文件路径是否有效

### 查看日志

```bash
tail -f gemini_content_generator.log
```

## 技术架构

- **API Key池管理**: 实现API Key轮询与失效检测
- **任务调度**: 循环遍历日期和版本，支持断点续跑
- **AI交互层**: 封装Gemini API调用逻辑，支持内容结构化解析
- **文件系统层**: 自动创建目录、保存文件，确保命名规范
- **异常与日志**: 详细的错误捕获、重试机制和进度日志
