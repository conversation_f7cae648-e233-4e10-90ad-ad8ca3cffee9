#!/usr/bin/env python3
"""
依赖安装脚本
自动安装Gemini内容生成器所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package.replace('-', '_'))
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🚀 Gemini内容生成器 - 依赖安装脚本")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        "google-generativeai"
    ]
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    print()
    
    # 升级pip
    print("📦 升级pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip升级成功")
    except subprocess.CalledProcessError:
        print("⚠️ pip升级失败，继续安装依赖...")
    
    print()
    
    # 安装依赖包
    success_count = 0
    for package in packages:
        if check_package(package):
            print(f"✅ {package} 已安装，跳过")
            success_count += 1
        else:
            if install_package(package):
                success_count += 1
    
    print()
    print("=" * 50)
    
    if success_count == len(packages):
        print("🎉 所有依赖安装完成！")
        print()
        print("📋 下一步操作:")
        print("1. 编辑 config.json 文件，添加你的Gemini API Key")
        print("2. 运行: python generate_gemini_content.py")
        print()
        print("📚 更多信息请查看 README.md")
        return True
    else:
        print(f"❌ 安装失败: {len(packages) - success_count}/{len(packages)} 个包安装失败")
        print()
        print("🔧 故障排除:")
        print("1. 检查网络连接")
        print("2. 尝试使用管理员权限运行")
        print("3. 手动安装: pip install google-generativeai")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断安装")
    except Exception as e:
        print(f"\n❌ 安装过程出错: {e}")
